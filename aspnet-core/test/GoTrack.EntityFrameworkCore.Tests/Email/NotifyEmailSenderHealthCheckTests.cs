using System;
using System.Threading;
using System.Threading.Tasks;
using GoTrack.Email;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Volo.Abp.Emailing.Smtp;
using Volo.Abp.Timing;
using Xunit;

namespace GoTrack.EntityFrameworkCore.Tests.Email;

public class NotifyEmailSenderHealthCheckTests
{
    private readonly ILogger<NotifyEmailSenderHealthCheck> _logger;
    private readonly EmailHealthRecordService _healthRecordService;
    private readonly ISmtpEmailSenderConfiguration _smtpConfiguration;
    private readonly IClock _clock;
    private readonly NotifyEmailSenderHealthCheck _healthCheck;

    public NotifyEmailSenderHealthCheckTests()
    {
        _logger = Substitute.For<ILogger<NotifyEmailSenderHealthCheck>>();
        _healthRecordService = new EmailHealthRecordService();
        _smtpConfiguration = Substitute.For<ISmtpEmailSenderConfiguration>();
        _clock = Substitute.For<IClock>();
        
        _clock.Now.Returns(DateTime.UtcNow);
        
        _healthCheck = new NotifyEmailSenderHealthCheck(
            _logger,
            _healthRecordService,
            _smtpConfiguration,
            _clock);
    }

    [Fact]
    public async Task CheckHealthAsync_Should_Return_Unhealthy_When_Host_Not_Configured()
    {
        // Arrange
        _smtpConfiguration.GetHostAsync().Returns(Task.FromResult<string>(null));
        _smtpConfiguration.GetPortAsync().Returns(Task.FromResult(587));
        _smtpConfiguration.GetUserNameAsync().Returns(Task.FromResult("<EMAIL>"));
        _smtpConfiguration.GetPasswordAsync().Returns(Task.FromResult("password"));

        // Act
        var result = await _healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        result.Status.ShouldBe(HealthStatus.Unhealthy);
        result.Description.ShouldBe("SMTP host is not configured");
    }

    [Fact]
    public async Task CheckHealthAsync_Should_Return_Unhealthy_When_Port_Invalid()
    {
        // Arrange
        _smtpConfiguration.GetHostAsync().Returns(Task.FromResult("smtp.example.com"));
        _smtpConfiguration.GetPortAsync().Returns(Task.FromResult(0));
        _smtpConfiguration.GetUserNameAsync().Returns(Task.FromResult("<EMAIL>"));
        _smtpConfiguration.GetPasswordAsync().Returns(Task.FromResult("password"));

        // Act
        var result = await _healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        result.Status.ShouldBe(HealthStatus.Unhealthy);
        result.Description.ShouldBe("SMTP port is not configured or invalid");
    }

    [Fact]
    public async Task CheckHealthAsync_Should_Return_Unhealthy_When_Username_Not_Configured()
    {
        // Arrange
        _smtpConfiguration.GetHostAsync().Returns(Task.FromResult("smtp.example.com"));
        _smtpConfiguration.GetPortAsync().Returns(Task.FromResult(587));
        _smtpConfiguration.GetUserNameAsync().Returns(Task.FromResult<string>(null));
        _smtpConfiguration.GetPasswordAsync().Returns(Task.FromResult("password"));

        // Act
        var result = await _healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        result.Status.ShouldBe(HealthStatus.Unhealthy);
        result.Description.ShouldBe("SMTP username is not configured");
    }

    [Fact]
    public async Task CheckHealthAsync_Should_Return_Unhealthy_When_Password_Not_Configured()
    {
        // Arrange
        _smtpConfiguration.GetHostAsync().Returns(Task.FromResult("smtp.example.com"));
        _smtpConfiguration.GetPortAsync().Returns(Task.FromResult(587));
        _smtpConfiguration.GetUserNameAsync().Returns(Task.FromResult("<EMAIL>"));
        _smtpConfiguration.GetPasswordAsync().Returns(Task.FromResult<string>(null));

        // Act
        var result = await _healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        result.Status.ShouldBe(HealthStatus.Unhealthy);
        result.Description.ShouldBe("SMTP password is not configured");
    }

    [Fact]
    public void EmailHealthRecord_Should_Create_Healthy_Record()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;
        var message = "Test message";

        // Act
        var record = EmailHealthRecord.CreateHealthy(timestamp, message);

        // Assert
        record.Timestamp.ShouldBe(timestamp);
        record.Status.ShouldBe(HealthStatus.Healthy);
        record.Message.ShouldBe(message);
        record.Exception.ShouldBeNull();
    }

    [Fact]
    public void EmailHealthRecord_Should_Create_Degraded_Record()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;
        var message = "Test message";
        var exception = new Exception("Test exception");

        // Act
        var record = EmailHealthRecord.CreateDegraded(timestamp, message, exception);

        // Assert
        record.Timestamp.ShouldBe(timestamp);
        record.Status.ShouldBe(HealthStatus.Degraded);
        record.Message.ShouldBe(message);
        record.Exception.ShouldBe(exception);
    }

    [Fact]
    public void EmailHealthRecord_Should_Create_Unhealthy_Record()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;
        var message = "Test message";
        var exception = new Exception("Test exception");

        // Act
        var record = EmailHealthRecord.CreateUnhealthy(timestamp, message, exception);

        // Assert
        record.Timestamp.ShouldBe(timestamp);
        record.Status.ShouldBe(HealthStatus.Unhealthy);
        record.Message.ShouldBe(message);
        record.Exception.ShouldBe(exception);
    }

    [Fact]
    public void EmailHealthRecordService_Should_Record_And_Retrieve_Health_Check()
    {
        // Arrange
        var timestamp = DateTime.UtcNow;
        var record = EmailHealthRecord.CreateHealthy(timestamp, "Test");

        // Act
        _healthRecordService.RecordHealthCheck(record);
        var currentStatus = _healthRecordService.GetCurrentStatus();

        // Assert
        currentStatus.ShouldNotBeNull();
        currentStatus.Status.ShouldBe(HealthStatus.Healthy);
        currentStatus.Message.ShouldBe("Test");
        _healthRecordService.IsHealthy().ShouldBeTrue();
    }
}
