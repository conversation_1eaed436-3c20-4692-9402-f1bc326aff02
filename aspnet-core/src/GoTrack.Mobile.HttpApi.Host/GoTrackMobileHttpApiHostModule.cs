using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using GoTrack.EntityFrameworkCore;
using GoTrack.Identity;
using GoTrack.Mobile.Options;
using GoTrack.Mobile.SwaggerHelpers;
using GoTrack.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Microsoft.OpenApi.Models;
using OpenIddict.Validation.AspNetCore;
using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.BlobStoring;
using Volo.Abp.Modularity;
using Volo.Abp.Swashbuckle;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Data;
using Volo.Abp.OpenIddict.ExtensionGrantTypes;
using System.Text.Json.Serialization;
using MassTransitLib;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using GoTrack.MtnSms;
using GoTrack.Email;
using GoTrack.Notifications;
using GoTrack.Options;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Volo.Abp.BackgroundWorkers;

namespace GoTrack.Mobile;

[DependsOn(
    typeof(GoTrackMobileHttpApiModule),
    typeof(AbpAutofacModule),
    typeof(AbpAspNetCoreMultiTenancyModule),
    typeof(GoTrackMobileApplicationModule),
    typeof(GoTrackEntityFrameworkCoreModule),
    //typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpSwashbuckleModule),
    typeof(MassTransitModule)
)]
public class GoTrackMobileHttpApiHostModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<OpenIddictBuilder>(builder =>
        {
            PreConfigure<OpenIddictServerBuilder>(builder =>
            {
                builder.Configure(options => options.GrantTypes.Add("otp"));
            });

            builder.AddValidation(options =>
            {
                options.AddAudiences("GoTrackMobile");
                options.UseLocalServer();
                options.UseAspNetCore();
            });
        });
        
        context.Services.AddOptions<FirebaseOptions>()
            .Bind(context.Services.GetConfiguration().GetSection("Firebase"))
            .ValidateDataAnnotations()
            .ValidateOnStart();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddHealthChecks()
            .AddCheck<MtnSmsHealthCheck>("mtn_sms_service", 
                tags: ["services", "sms"]);
        
        context.Services.AddHealthChecks()
            .AddCheck<FirebaseNotificationCheckHealth>("fcm_notification_service", 
                tags: ["services", "fcm"]);
        
        context.Services.AddHealthChecksUI(config =>
        {
            config.AddHealthCheckEndpoint("GoTrackMobile", "/health");
            config.UseApiEndpointHttpMessageHandler(sp =>
            {
                return new HttpClientHandler
                {
                    ClientCertificateOptions = ClientCertificateOption.Manual,
                    ServerCertificateCustomValidationCallback = (req, cert, chain, errors) => true
                };
            });
        }).AddInMemoryStorage();

        
        Configure<DataProtectionTokenProviderOptions>(options =>
        {
            options.Name = TokenOptions.DefaultEmailProvider;
            options.TokenLifespan = TimeSpan.FromHours(1);
        });

        Configure<AbpBackgroundJobOptions>(options => { options.IsJobExecutionEnabled = false; });
        Configure<AbpBackgroundWorkerOptions>(options => { options.IsEnabled = false; });
        Configure<AbpOpenIddictExtensionGrantsOptions>(opt => { opt.Grants.Add("otp", new OtpTokenExtensionGrant()); });

        Configure<AbpDataFilterOptions>(options =>
        {
            options.DefaultStates[typeof(ICustomerUserFilter)] = new DataFilterState(isEnabled: true);
            options.DefaultStates[typeof(IHostTenantUserFilter)] = new DataFilterState(isEnabled: false);
        });

        var serviceProvider = context.Services.BuildServiceProvider();
        var fcmOptions = serviceProvider.GetRequiredService<IOptions<FirebaseOptions>>().Value;

        if (FirebaseApp.DefaultInstance is null)
        {
            var credential = GoogleCredential.FromJsonParameters(new JsonCredentialParameters
            {
                Type = fcmOptions.Type,
                ProjectId = fcmOptions.ProjectId,
                PrivateKeyId = fcmOptions.PrivateKeyId,
                PrivateKey = fcmOptions.PrivateKey,
                ClientEmail = fcmOptions.ClientEmail,
                ClientId = fcmOptions.ClientId,
                TokenUri = fcmOptions.TokenUri,
                UniverseDomain = fcmOptions.UniverseDomain
            });
            
            FirebaseApp.Create(new AppOptions
            {
                Credential = credential,
                ProjectId = fcmOptions.ProjectId,
            });
        }

        var configuration = context.Services.GetConfiguration();
        var hostingEnvironment = context.Services.GetHostingEnvironment();

        context.Services.Configure<DevelopmentOptions>(configuration.GetSection("Development"));

        ConfigureAuthentication(context);
        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureConventionalControllers();
        ConfigureVirtualFileSystem(context);
        ConfigureCors(context, configuration);
        ConfigureSwaggerServices(context, configuration);

        context.Services.Configure<AbpAntiForgeryOptions>(opt => { opt.AutoValidate = false; });

        context.Services.AddControllers()
           .AddJsonOptions(jo => { jo.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()); });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context)
    {
        context.Services.ForwardIdentityAuthenticationForBearer(OpenIddictValidationAspNetCoreDefaults
            .AuthenticationScheme);
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle => { bundle.AddFiles("/global-styles.css"); }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
            options.RedirectAllowedUrls.AddRange(configuration["App:RedirectAllowedUrls"]?.Split(',') ??
                                                 Array.Empty<string>());

            options.Applications["Angular"].RootUrl = configuration["App:ClientUrl"];
            options.Applications["Angular"].Urls[AccountUrlNames.PasswordReset] = "account/reset-password";
        });
    }

    private void ConfigureVirtualFileSystem(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();

        if (hostingEnvironment.IsDevelopment())
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.ReplaceEmbeddedByPhysical<GoTrackDomainSharedModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}GoTrack.Domain.Shared"));
                options.FileSets.ReplaceEmbeddedByPhysical<GoTrackDomainModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}GoTrack.Domain"));
                options.FileSets.ReplaceEmbeddedByPhysical<GoTrackApplicationContractsModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}GoTrack.Mobile.Application.Contracts"));
                options.FileSets.ReplaceEmbeddedByPhysical<GoTrackMobileApplicationModule>(
                    Path.Combine(hostingEnvironment.ContentRootPath,
                        $"..{Path.DirectorySeparatorChar}GoTrack.Mobile.Application"));
            });
        }
    }

    private void ConfigureConventionalControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(GoTrackMobileApplicationModule).Assembly,
                opts => { opts.UseV3UrlStyle = true; });
            //options.ConventionalControllers.Create(typeof(FCMApplicationModule).Assembly);

            // var controllersToRemove = new HashSet<Type>
            // {
            //     typeof(EmailSettingsController),
            //     typeof(TimeZoneSettingsController),
            //     typeof(TenantController),
            //     typeof(FeaturesController),
            //     typeof(PermissionsController),
            //     typeof(AccountController),
            //     typeof(IdentityUserLookupController),
            //     typeof(IdentityUserController),
            //     typeof(IdentityRoleController),
            // };
            // foreach (var controllerToRemove in controllersToRemove)
            // {
            //     options.ControllersToRemove.AddIfNotContains(controllerToRemove);
            // }
        });
    }

    private static void ConfigureSwaggerServices(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddSwaggerGen(options =>
            {
                options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Description =
                        "JWT Authorization header using the Bearer scheme. \r\n\r\n Enter your token in the text input below",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                    Scheme = "Bearer"
                });
                options.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            },
                            Scheme = "oauth2",
                            Name = "Bearer",
                            In = ParameterLocation.Header,
                        },
                        new List<string>()
                    }
                });

                options.OperationFilter<LocalizationFilter>();
                options.OperationFilter<TrackAccountFilter>();
                options.OperationFilter<TrackAccountOrObserverFilter>();
                options.DocumentFilter<DocumentFilter>();
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "GoTrack Mobile APIs", Version = "v1" });
                options.DocInclusionPredicate((_, _) => true);
                options.CustomSchemaIds(type => type.FullName);
            }
        );
    }

    private void ConfigureCors(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(configuration["App:CorsOrigins"]?
                        .Split(",", StringSplitOptions.RemoveEmptyEntries)
                        .Select(o => o.RemovePostFix("/"))
                        .ToArray() ?? Array.Empty<string>())
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        app.UseCorrelationId();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseCors();
        app.UseAuthentication();
        app.UseAbpOpenIddictValidation();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseAuthorization();

        app.UseSwagger();
        app.UseAbpSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "GoTrack API");
            var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
            c.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
            c.OAuthScopes("GoTrack");
        });

        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapHealthChecks("/health",new HealthCheckOptions
            {
                Predicate = _ => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });
        });
        app.UseHealthChecksUI(config =>
        {
            config.UIPath = "/hc-ui";
        }); 

    }
}