using System;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using GoTrack.MtnSms.Options;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Settings;
using Volo.Abp.Sms;
using Volo.Abp.Timing;

namespace GoTrack.MtnSms;

public partial class MtnSmsSender : ISmsSender, ITransientDependency
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ISettingProvider _settingProvider;
    private readonly MtnHealthRecordService _healthRecordService;
    private readonly IClock _clock;
    
    public MtnSmsSender(
        IHttpClientFactory httpClientFactory, 
        ISettingProvider settingProvider, 
        MtnHealthRecordService healthRecordService, 
        IClock clock)
    {
        _httpClientFactory = httpClientFactory;
        _settingProvider = settingProvider;
        _healthRecordService = healthRecordService;
        _clock = clock;
    }
    
    public async Task SendAsync(SmsMessage smsMessage)
    {
        try
        {
            await InnerSendAsync(smsMessage);
            _healthRecordService.RecordHealthCheck(
                MtnHealthRecord.CreateHealthy(_clock.Now, "SMS sent successfully"));
        }
        catch (Exception ex)
        {
            _healthRecordService.RecordHealthCheck(
                MtnHealthRecord.CreateDegraded(_clock.Now, $"Failed to send SMS: {ex.Message}", ex));
            throw;
        }
    }
    
    private async Task InnerSendAsync(SmsMessage smsMessage)
    {
        await ValidateSettings();

        var senderId = await _settingProvider.GetSenderIdOfMtnSmsSender();
        var username = await _settingProvider.GetUsernameOfMtnSmsSender();
        var password = await _settingProvider.GetPasswordOfMtnSmsSender();

        var language = DetectLanguage(smsMessage.Text);
        var phoneNumberWith12digits = ConvertTo12digits(smsMessage.PhoneNumber);
        var encodedMessage = ConvertToUnicodeHex(smsMessage.Text);
        
        var queryString = $"?User={username}&Pass={password}" +
                          $"&From={senderId}" +
                          $"&Gsm={string.Join(";", phoneNumberWith12digits)}" +
                          $"&Lang={language}" +
                          $"&Msg={Uri.EscapeDataString(encodedMessage)}";

        var client = CreateHttpClient();
        var response = await client.GetAsync($"{MtnSmsConstants.ApiSendEndpoint}{queryString}");

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"SMS sending failed: {response.StatusCode} - {response.ReasonPhrase}");
        }
    }

    internal async Task ValidateSettings()
    {
        if (await _settingProvider.IsMtnSmsSenderSettingExist() is false)
            throw new Exception("MtnSmsSender Not Configured");

        var senderId = await _settingProvider.GetSenderIdOfMtnSmsSender();
        var username = await _settingProvider.GetUsernameOfMtnSmsSender();
        var password = await _settingProvider.GetPasswordOfMtnSmsSender();

        if (string.IsNullOrEmpty(senderId) ||
            string.IsNullOrEmpty(username) ||
            string.IsNullOrEmpty(password))
        {
            throw new Exception("Required MTN SMS settings are missing");
        }
    }

    internal HttpClient CreateHttpClient()
    {
        return _httpClientFactory.CreateClient();
    }

    private static string ConvertTo12digits(string smsMessagePhoneNumber)
    {
        if (string.IsNullOrEmpty(smsMessagePhoneNumber))
            throw new ArgumentException("Phone number cannot be null or empty", nameof(smsMessagePhoneNumber));

        if (smsMessagePhoneNumber.Length == 12 && smsMessagePhoneNumber.StartsWith("9639"))
            return smsMessagePhoneNumber;

        if (smsMessagePhoneNumber.Length == 14 && smsMessagePhoneNumber.StartsWith("009639"))
        {
            return smsMessagePhoneNumber[2..];
        }

        throw new ArgumentException(
            $"Invalid Syrian mobile number format: {smsMessagePhoneNumber}. " +
            "Expected formats: 9XXXXXXXX, 9639XXXXXXXX, 009639XXXXXXXX, or +9639XXXXXXXX",
            nameof(smsMessagePhoneNumber));
    }

    [GeneratedRegex(@"\p{IsArabic}")]
    private static partial Regex ArabicRegex();

    private static int DetectLanguage(string message)
    {
        return ArabicRegex().IsMatch(message) ? 0 : 1;
    }

    private static string ConvertToUnicodeHex(string input)
    {
        return string.Concat(input.Select(c => ((int)c).ToString("X4")));
    }
}