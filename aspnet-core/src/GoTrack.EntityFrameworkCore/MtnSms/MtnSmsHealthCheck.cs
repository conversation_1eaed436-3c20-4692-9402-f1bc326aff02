using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Timing;

namespace GoTrack.MtnSms;

public class MtnSmsHealthCheck : IHealthCheck
{
    private readonly ILogger<MtnSmsHealthCheck> _logger;
    private readonly MtnHealthRecordService _healthRecordService;
    private readonly MtnSmsSender _mtnSmsSender;
    private readonly IClock _clock;

    public MtnSmsHealthCheck(
        ILogger<MtnSmsHealthCheck> logger, 
        MtnHealthRecordService healthRecordService,
        MtnSmsSender mtnSmsSender,
        IClock clock)
    {
        _logger = logger;
        _healthRecordService = healthRecordService;
        _mtnSmsSender = mtnSmsSender;
        _clock = clock;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            try
            {
                await _mtnSmsSender.ValidateSettings();
            }
            catch (Exception ex)
            {
                var result = HealthCheckResult.Unhealthy(ex.Message, ex);
                
                _healthRecordService.RecordHealthCheck(
                    MtnHealthRecord.CreateUnhealthy(_clock.Now, ex.Message, ex));
                return result;
            }

            var apiCheckResult = await CheckApiEndpointAvailabilityAsync(cancellationToken);
            if (!apiCheckResult.IsSuccessStatusCode)
            {
                var message = $"MTN API endpoint unavailable. Status: {apiCheckResult.StatusCode}";
                _logger.LogWarning(message);
                
                var result = HealthCheckResult.Unhealthy(message);
                _healthRecordService.RecordHealthCheck(
                    MtnHealthRecord.CreateUnhealthy(_clock.Now, message));
                return result;
            }            
            
            var currentState = _healthRecordService.GetCurrentStatus();
            if (currentState is not null && currentState.Status != HealthStatus.Healthy)
            {
                return HealthCheckResult.Degraded(
                    currentState.Message, 
                    currentState.Exception,
                    new Dictionary<string, object>
                    {
                        ["lastHealthyTimestamp"] = _healthRecordService.GetLastHealthyTimestamp()?.ToString("o") ?? "never",
                        ["statusSince"] = currentState.Timestamp.ToString("o")
                    });
            }

            var healthyResult = HealthCheckResult.Healthy("MTN SMS service is operational");
            _healthRecordService.RecordHealthCheck(
                MtnHealthRecord.CreateHealthy(_clock.Now, "Health check passed"));
            return healthyResult;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "MTN SMS API connectivity error");
            var result = HealthCheckResult.Degraded("MTN API communication error", ex);
            _healthRecordService.RecordHealthCheck(
                MtnHealthRecord.CreateDegraded(_clock.Now, "API communication error", ex));
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MTN SMS health check failed");
            var result = HealthCheckResult.Unhealthy("MTN SMS service check failed", ex);
            _healthRecordService.RecordHealthCheck(
                MtnHealthRecord.CreateUnhealthy(_clock.Now, "Health check failed", ex));
            return result;
        }
    }
    
    private async Task<HttpResponseMessage> CheckApiEndpointAvailabilityAsync(CancellationToken cancellationToken)
    {
        var client = _mtnSmsSender.CreateHttpClient();
        client.Timeout = TimeSpan.FromSeconds(5); 

        var headResponse = await client.SendAsync(
            new HttpRequestMessage(HttpMethod.Head, MtnSmsConstants.ApiBaseUrl),
            cancellationToken);

        return headResponse;
    }
}
