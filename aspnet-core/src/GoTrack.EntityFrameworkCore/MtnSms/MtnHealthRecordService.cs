using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Volo.Abp.DependencyInjection;

namespace GoTrack.MtnSms;

public class MtnHealthRecordService : ISingletonDependency
{
    private List<MtnHealthRecord> _records = [];
    private const int MaxRecords = 100;


    public void RecordHealthCheck(MtnHealthRecord healthRecord)
    {
        var last = _records.LastOrDefault();
        
        if (last is not null && last.Timestamp > healthRecord.Timestamp)
        {
            throw new ArgumentException("Out-of-order health record detected. Last: {LastTimestamp}, New: {NewTimestamp}");
        }

        if (_records.Count >= MaxRecords)
        {
            _records.RemoveAt(0);
        }
        
        _records.Add(healthRecord);
    }

    public bool IsHealthy()
    {
        return _records.LastOrDefault()?.Status == HealthStatus.Healthy;
    }
    
    public MtnHealthRecord? GetCurrentStatus()
    {
        return _records.LastOrDefault();
    }
    
    public IReadOnlyList<MtnHealthRecord> GetRecords()
    {
        return _records.AsReadOnly();
    }

    public DateTime? GetLastHealthyTimestamp()
    {
        return _records
            .Where(r => r.Status == HealthStatus.Healthy)
            .OrderByDescending(r => r.Timestamp)
            .FirstOrDefault()?.Timestamp;
    }
}
