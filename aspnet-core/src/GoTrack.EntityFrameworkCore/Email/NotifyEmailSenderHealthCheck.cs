using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Emailing.Smtp;
using Volo.Abp.Timing;

namespace GoTrack.Email;

public class NotifyEmailSenderHealthCheck : IHealthCheck
{
    private readonly ILogger<NotifyEmailSenderHealthCheck> _logger;
    private readonly EmailHealthRecordService _healthRecordService;
    private readonly ISmtpEmailSenderConfiguration _smtpConfiguration;
    private readonly IClock _clock;

    public NotifyEmailSenderHealthCheck(
        ILogger<NotifyEmailSenderHealthCheck> logger,
        EmailHealthRecordService healthRecordService,
        ISmtpEmailSenderConfiguration smtpConfiguration,
        IClock clock)
    {
        _logger = logger;
        _healthRecordService = healthRecordService;
        _smtpConfiguration = smtpConfiguration;
        _clock = clock;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            try
            {
                await ValidateSmtpConfiguration();
            }
            catch (Exception ex)
            {
                var result = HealthCheckResult.Unhealthy(ex.Message, ex);
                
                _healthRecordService.RecordHealthCheck(
                    EmailHealthRecord.CreateUnhealthy(_clock.Now, ex.Message, ex));
                return result;
            }

            var connectionCheckResult = await CheckSmtpConnectionAsync(cancellationToken);
            if (!connectionCheckResult.IsSuccessful)
            {
                var message = $"SMTP server connection failed: {connectionCheckResult.ErrorMessage}";
                _logger.LogWarning(message);
                
                var result = HealthCheckResult.Unhealthy(message, connectionCheckResult.Exception);
                _healthRecordService.RecordHealthCheck(
                    EmailHealthRecord.CreateUnhealthy(_clock.Now, message, connectionCheckResult.Exception));
                return result;
            }

            var currentState = _healthRecordService.GetCurrentStatus();
            if (currentState is not null && currentState.Status != HealthStatus.Healthy)
            {
                return HealthCheckResult.Degraded(
                    currentState.Message,
                    currentState.Exception,
                    new Dictionary<string, object>
                    {
                        ["lastHealthyTimestamp"] = _healthRecordService.GetLastHealthyTimestamp()?.ToString("o") ?? "never",
                        ["statusSince"] = currentState.Timestamp.ToString("o")
                    });
            }

            var healthyResult = HealthCheckResult.Healthy("Email service is operational");
            _healthRecordService.RecordHealthCheck(
                EmailHealthRecord.CreateHealthy(_clock.Now, "Health check passed"));
            return healthyResult;
        }
        catch (SocketException ex)
        {
            _logger.LogWarning(ex, "Email SMTP connectivity error");
            var result = HealthCheckResult.Degraded("SMTP communication error", ex);
            _healthRecordService.RecordHealthCheck(
                EmailHealthRecord.CreateDegraded(_clock.Now, "SMTP communication error", ex));
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Email service health check failed");
            var result = HealthCheckResult.Unhealthy("Email service check failed", ex);
            _healthRecordService.RecordHealthCheck(
                EmailHealthRecord.CreateUnhealthy(_clock.Now, "Health check failed", ex));
            return result;
        }
    }

    private async Task ValidateSmtpConfiguration()
    {
        var host = await _smtpConfiguration.GetHostAsync();
        var port = await _smtpConfiguration.GetPortAsync();
        var userName = await _smtpConfiguration.GetUserNameAsync();
        var password = await _smtpConfiguration.GetPasswordAsync();

        if (string.IsNullOrEmpty(host))
            throw new Exception("SMTP host is not configured");

        if (port <= 0)
            throw new Exception("SMTP port is not configured or invalid");

        if (string.IsNullOrEmpty(userName))
            throw new Exception("SMTP username is not configured");

        if (string.IsNullOrEmpty(password))
            throw new Exception("SMTP password is not configured");
    }

    private async Task<SmtpConnectionResult> CheckSmtpConnectionAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var client = new SmtpClient();
            client.Timeout = 10000; // 10 seconds timeout
            client.ServerCertificateValidationCallback = (sender, certificate, chain, errors) => true;

            var host = await _smtpConfiguration.GetHostAsync();
            var port = await _smtpConfiguration.GetPortAsync();
            var enableSsl = await _smtpConfiguration.GetEnableSslAsync();
            var userName = await _smtpConfiguration.GetUserNameAsync();
            var password = await _smtpConfiguration.GetPasswordAsync();

            // Connect to SMTP server
            var secureSocketOptions = enableSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTlsWhenAvailable;
            await client.ConnectAsync(host, port, secureSocketOptions, cancellationToken);

            // Authenticate if credentials are provided
            if (!string.IsNullOrEmpty(userName) && !string.IsNullOrEmpty(password))
            {
                await client.AuthenticateAsync(userName, password, cancellationToken);
            }

            await client.DisconnectAsync(true, cancellationToken);

            return SmtpConnectionResult.Success();
        }
        catch (Exception ex)
        {
            return SmtpConnectionResult.Failure(ex.Message, ex);
        }
    }

    private class SmtpConnectionResult
    {
        public bool IsSuccessful { get; private set; }
        public string? ErrorMessage { get; private set; }
        public Exception? Exception { get; private set; }

        private SmtpConnectionResult() { }

        public static SmtpConnectionResult Success()
        {
            return new SmtpConnectionResult { IsSuccessful = true };
        }

        public static SmtpConnectionResult Failure(string errorMessage, Exception? exception = null)
        {
            return new SmtpConnectionResult 
            { 
                IsSuccessful = false, 
                ErrorMessage = errorMessage, 
                Exception = exception 
            };
        }
    }
}
