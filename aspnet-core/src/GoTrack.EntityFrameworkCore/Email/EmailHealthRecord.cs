using System;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace GoTrack.Email;

public class EmailHealthRecord
{
    public DateTime Timestamp { get; }
    public HealthStatus Status { get; }
    public string Message { get; }
    public Exception? Exception { get; }

    private EmailHealthRecord(DateTime timestamp, HealthStatus status, string message, Exception? exception = null)
    {
        Timestamp = timestamp;
        Status = status;
        Message = message;
        Exception = exception;
    }

    public static EmailHealthRecord CreateHealthy(DateTime timestamp, string message = "Service is healthy")
    {
        return new EmailHealthRecord(timestamp, HealthStatus.Healthy, message);
    }

    public static EmailHealthRecord CreateDegraded(DateTime timestamp, string message, Exception? exception = null)
    {
        return new EmailHealthRecord(timestamp, HealthStatus.Degraded, message, exception);
    }

    public static EmailHealthRecord CreateUnhealthy(DateTime timestamp, string message, Exception? exception = null)
    {
        return new EmailHealthRecord(timestamp, HealthStatus.Unhealthy, message, exception);
    }
}
