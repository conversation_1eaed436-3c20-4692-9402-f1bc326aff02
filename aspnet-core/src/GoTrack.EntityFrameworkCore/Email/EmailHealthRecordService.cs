using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Volo.Abp.DependencyInjection;

namespace GoTrack.Email;

public class EmailHealthRecordService : ISingletonDependency
{
    private List<EmailHealthRecord> _records = [];
    private const int MaxRecords = 100;

    private readonly ReaderWriterLockSlim _lock = new();

    public void RecordHealthCheck(EmailHealthRecord emailHealthRecord)
    {
        _lock.EnterWriteLock();
        try
        {
            var last = _records.LastOrDefault();

            if (last is not null && last.Timestamp > emailHealthRecord.Timestamp)
            {
                throw new ArgumentException(
                    $"Out-of-order health record detected. Last: {last.Timestamp}, New: {emailHealthRecord.Timestamp}");
            }

            if (_records.Count >= MaxRecords)
            {
                _records.RemoveAt(0);
            }

            _records.Add(emailHealthRecord);
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    public bool IsHealthy()
    {
        _lock.EnterReadLock();
        try
        {
            return _records.LastOrDefault()?.Status == HealthStatus.Healthy;
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }

    public EmailHealthRecord? GetCurrentStatus()
    {
        _lock.EnterReadLock();
        try
        {
            return _records.LastOrDefault();
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }

    public IReadOnlyList<EmailHealthRecord> GetRecords()
    {
        _lock.EnterReadLock();
        try
        {
            return _records.ToList();
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }

    public DateTime? GetLastHealthyTimestamp()
    {
        _lock.EnterReadLock();
        try
        {
            return _records
                .Where(r => r.Status == HealthStatus.Healthy)
                .LastOrDefault()?.Timestamp;
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }
}
