using System;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace GoTrack.Notifications;

public class FirebaseNotificationHealthRecord
{
    public DateTime Timestamp { get; }
    public HealthStatus Status { get; }
    public string Message { get; }
    public Exception? Exception { get; }

    private FirebaseNotificationHealthRecord(DateTime timestamp, HealthStatus status, string message, Exception? exception = null)
    {
        Timestamp = timestamp;
        Status = status;
        Message = message;
        Exception = exception;
    }

    public static FirebaseNotificationHealthRecord CreateHealthy(DateTime timestamp, string message = "Service is healthy")
    {
        return new FirebaseNotificationHealthRecord(timestamp, HealthStatus.Healthy, message);
    }

    public static FirebaseNotificationHealthRecord CreateDegraded(DateTime timestamp, string message, Exception? exception = null)
    {
        return new FirebaseNotificationHealthRecord(timestamp, HealthStatus.Degraded, message, exception);
    }

    public static FirebaseNotificationHealthRecord CreateUnhealthy(DateTime timestamp, string message, Exception? exception = null)
    {
        return new FirebaseNotificationHealthRecord(timestamp, HealthStatus.Unhealthy, message, exception);
    }
}