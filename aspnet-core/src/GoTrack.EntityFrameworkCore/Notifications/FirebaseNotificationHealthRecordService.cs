using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Volo.Abp.DependencyInjection;

namespace GoTrack.Notifications;

public class FirebaseNotificationHealthRecordService : ISingletonDependency
{
    private List<FirebaseNotificationHealthRecord> _records = [];
    private const int MaxRecords = 100;

    private readonly ReaderWriterLockSlim _lock = new();

    public void RecordHealthCheck(FirebaseNotificationHealthRecord notificationHealthRecord)
    {
        _lock.EnterWriteLock();
        try
        {
            var last = _records.LastOrDefault();

            if (last is not null && last.Timestamp > notificationHealthRecord.Timestamp)
            {
                throw new ArgumentException(
                    $"Out-of-order health record detected. Last: {last.Timestamp}, New: {notificationHealthRecord.Timestamp}");
            }

            if (_records.Count >= MaxRecords)
            {
                _records.RemoveAt(0);
            }

            _records.Add(notificationHealthRecord);
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    public bool IsHealthy()
    {
        _lock.EnterReadLock();
        try
        {
            return _records.LastOrDefault()?.Status == HealthStatus.Healthy;
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }

    public FirebaseNotificationHealthRecord? GetCurrentStatus()
    {
        _lock.EnterReadLock();
        try
        {
            return _records.LastOrDefault();
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }

    public IReadOnlyList<FirebaseNotificationHealthRecord> GetRecords()
    {
        _lock.EnterReadLock();
        try
        {
            return _records.AsReadOnly();
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }

    public DateTime? GetLastHealthyTimestamp()
    {
        _lock.EnterReadLock();
        try
        {
            return _records
                .Where(r => r.Status == HealthStatus.Healthy)
                .OrderByDescending(r => r.Timestamp)
                .FirstOrDefault()?.Timestamp;
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }
}