using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using FirebaseAdmin.Messaging;
using GoTrack.BackgroundJobs.JobArguments;
using GoTrack.FCMDevices;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Timing;

namespace GoTrack.Notifications;

public class FirebaseNotificationSender : ITransientDependency, IFirebaseNotificationSender
{
    private readonly IBackgroundJobManager _backgroundJobManager;
    private readonly ILogger<FirebaseNotificationSender> _logger;
    private readonly FcmDeviceManager _fcmDeviceManager;
    private readonly FirebaseNotificationHealthRecordService _firebaseNotificationHealthRecord;
    private readonly IClock _clock;

    public FirebaseNotificationSender(IBackgroundJobManager backgroundJobManager,
        ILogger<FirebaseNotificationSender> logger, FcmDeviceManager fcmDeviceManager,
        FirebaseNotificationHealthRecordService firebaseNotificationHealthRecord, IClock clock)
    {
        _backgroundJobManager = backgroundJobManager;
        _logger = logger;
        _fcmDeviceManager = fcmDeviceManager;
        _firebaseNotificationHealthRecord = firebaseNotificationHealthRecord;
        _clock = clock;
    }

    public async Task<IEnumerable<string>> SendPushNotificationAsync(string title, string body, List<string> fcmTokens,
        Dictionary<string, string>? data = null)
    {
        if (fcmTokens.Count is 0) return [];

        const int size = 500;
        var batches = fcmTokens.Chunk(size);

        var failedTokens = new List<string>();
        var invalidTokens = new List<string>();

        await Parallel.ForEachAsync(batches, async (batch, cancellationToken) =>
        {
            var message = new MulticastMessage
            {
                Tokens = [.. batch],
                Notification = new Notification { Title = title, Body = body },
                Data = data ?? []
            };

            try
            {
                var response =
                    await FirebaseMessaging.DefaultInstance.SendEachForMulticastAsync(message, cancellationToken);

                for (int i = 0; i < response.Responses.Count; i++)
                {
                    if (response.Responses[i].IsSuccess)
                    {
                        continue;
                    }

                    var failedToken = batch.ElementAt(i);
                    var error = response.Responses[i].Exception;

                    if (error is FirebaseMessagingException fcmException)
                    {
                        _logger.LogError(
                            "Push notification failed for token {Token}. Error: {ErrorCode} - {ErrorMessage}",
                            failedToken, fcmException.MessagingErrorCode, fcmException.Message);
                        switch (fcmException.MessagingErrorCode)
                        {
                            case MessagingErrorCode.QuotaExceeded:
                                _firebaseNotificationHealthRecord.RecordHealthCheck(
                                    FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now,
                                        "FCM quota exceeded", fcmException));
                                failedTokens.Add(failedToken);
                                break;

                            case MessagingErrorCode.Unavailable:
                                _firebaseNotificationHealthRecord.RecordHealthCheck(
                                    FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now,
                                        "FCM service unavailable", fcmException));
                                failedTokens.Add(failedToken);
                                break;

                            case MessagingErrorCode.SenderIdMismatch:
                            case MessagingErrorCode.Unregistered:
                            case MessagingErrorCode.InvalidArgument:
                            case MessagingErrorCode.Internal:
                                invalidTokens.Add(failedToken);
                                break;

                            case MessagingErrorCode.ThirdPartyAuthError:
                                _firebaseNotificationHealthRecord.RecordHealthCheck(
                                    FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now,
                                        "FCM third-party auth error", fcmException));
                                break;

                            case null:
                                _firebaseNotificationHealthRecord.RecordHealthCheck(
                                    FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now,
                                        "Unknown FCM error", fcmException));
                                break;

                            default:
                                _firebaseNotificationHealthRecord.RecordHealthCheck(
                                    FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now,
                                        $"Unhandled FCM error: {fcmException.MessagingErrorCode}", fcmException));
                                throw new ArgumentOutOfRangeException();
                        }
                    }
                }

                if (invalidTokens.Count is not 0)
                {
                    _ = invalidTokens.Select(async invalidToken =>
                        await _fcmDeviceManager.DeactivateDeviceAsync(invalidToken));
                }

                if (failedTokens.Count is not 0)
                {
                    await _backgroundJobManager.EnqueueAsync(new ResendPushNotificationArgs
                    {
                        Title = title,
                        Body = body,
                        FcmTokens = failedTokens,
                        Data = data
                    });
                }
                else
                {
                    _firebaseNotificationHealthRecord.RecordHealthCheck(
                        FirebaseNotificationHealthRecord.CreateHealthy(_clock.Now));
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError("Network error,sending to background job for retry");

                _firebaseNotificationHealthRecord.RecordHealthCheck(
                    FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now,
                        "Network error sending FCM notifications", ex));

                await _backgroundJobManager.EnqueueAsync(new ResendPushNotificationArgs
                {
                    Title = title,
                    Body = body,
                    FcmTokens = [.. batch],
                    Data = data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Unexpected error: {ex.Message}");
                _firebaseNotificationHealthRecord.RecordHealthCheck(
                    FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now,
                        "Unexpected error sending FCM notifications", ex));
            }
        });

        return invalidTokens.Concat(failedTokens);
    }
}