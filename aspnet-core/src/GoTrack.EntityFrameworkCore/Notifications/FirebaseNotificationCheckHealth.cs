using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using FirebaseAdmin.Messaging;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Timing;

namespace GoTrack.Notifications;

public class FirebaseNotificationCheckHealth : IHealthCheck
{
    private readonly ILogger<FirebaseNotificationCheckHealth> _logger;
    private readonly FirebaseNotificationHealthRecordService _healthRecordService;
    private readonly IClock _clock;

    public FirebaseNotificationCheckHealth(
        ILogger<FirebaseNotificationCheckHealth> logger, 
        FirebaseNotificationHealthRecordService healthRecordService,
        IClock clock)
    {
        _logger = logger;
        _healthRecordService = healthRecordService;
        _clock = clock;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Performing Firebase health check");
            
            var response = await FirebaseMessaging.DefaultInstance.SendAsync(new Message
            {
                Topic = "health-check",
                Data = new Dictionary<string, string>
                {
                    {"healthcheck", "ping"}
                }
            }, cancellationToken);
            
            var currentState = _healthRecordService.GetCurrentStatus();
            
            if (currentState is not null && currentState.Status != HealthStatus.Healthy)
            {
                return HealthCheckResult.Unhealthy(
                    currentState.Message, 
                    currentState.Exception,
                    new Dictionary<string, object>
                    {
                        ["lastHealthyTimestamp"] = _healthRecordService.GetLastHealthyTimestamp()?.ToString("o") ?? "never",
                        ["statusSince"] = currentState.Timestamp.ToString("o")
                    });
            }

            var healthyResult = HealthCheckResult.Healthy("Firebase notification service is operational");
            _healthRecordService.RecordHealthCheck(
                FirebaseNotificationHealthRecord.CreateHealthy(
                    _clock.Now,
                    "Health check passed")
            );
            
            return healthyResult;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "Firebase API connectivity error");
            var result = HealthCheckResult.Degraded("Firebase API communication error", ex);
            _healthRecordService.RecordHealthCheck(
                FirebaseNotificationHealthRecord.CreateDegraded(_clock.Now, "API communication error", ex));
            return result;
        }
        catch (FirebaseMessagingException ex)
        {
            _logger.LogError(ex, "Firebase messaging error during health check: {ErrorCode}", ex.MessagingErrorCode);
            var result = HealthCheckResult.Unhealthy($"Firebase service error: {ex.MessagingErrorCode}", ex);
            _healthRecordService.RecordHealthCheck(
                FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now, $"Firebase error: {ex.MessagingErrorCode}", ex));
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error checking Firebase health");
            var result = HealthCheckResult.Unhealthy("Firebase service check failed", ex);
            _healthRecordService.RecordHealthCheck(
                FirebaseNotificationHealthRecord.CreateUnhealthy(_clock.Now, "Health check failed", ex));
            return result;
        }
    }
}